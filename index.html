<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Our Eternal Love Story</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link
        href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=Quicksand:wght@400;600&family=Playfair+Display:wght@400;700&display=swap"
        rel="stylesheet">
    <link rel="icon"
        href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='0.9em' font-size='90'%3E%F0%9F%92%96%3C/text%3E%3C/svg%3E">
    <style>
        :root {
            --bg-1: #ffe6f2;
            --bg-2: #ffd7ef;
            --ink: #5c4b51;
            --accent: #ff9ec7;
            --accent-2: #ffd1e6;
            --glass: rgba(255, 255, 255, 0.55);
            --radius: 280px;
            --card-w: 170px;
            --card-h: 170px;
            --gap-y: 35px;
            --shadow: 0 10px 25px rgba(255, 105, 180, 0.25);
            --gold: linear-gradient(135deg, #f6e6b4 0%, #edc281 100%);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html,
        body {
            height: 100%;
            margin: 0;
            font-family: "Quicksand", system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            color: var(--ink);
            background: radial-gradient(1000px 800px at 20% 10%, var(--bg-2), transparent 60%),
                radial-gradient(900px 900px at 80% 10%, #ffeef7, transparent 60%),
                linear-gradient(180deg, var(--bg-1), #fff0f6);
            overflow: hidden;
        }

        .wrap {
            position: relative;
            height: 100%;
            width: 100%;
            perspective: 1400px;
            display: grid;
            grid-template-rows: minmax(auto, 200px) 1fr minmax(auto, 60px);
            padding: 0 10px;
        }

        header {
            padding: 15px clamp(16px, 4vw, 40px) 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            justify-content: center;
            text-align: center;
        }

        .title {
            font-family: "Great Vibes", cursive;
            font-size: clamp(42px, 7vw, 68px);
            line-height: 1;
            color: #c34d7f;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 0 #ffffffaa;
            margin: 0;
            background: var(--gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 2px 3px rgba(195, 77, 127, 0.2));
        }

        .subtitle {
            font-family: "Playfair Display", serif;
            font-size: clamp(18px, 3vw, 24px);
            color: #7b4e63;
            margin-top: -10px;
            font-weight: 400;
        }

        .controls {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 10px;
        }

        .btn {
            appearance: none;
            border: none;
            padding: 12px 20px;
            border-radius: 999px;
            background: linear-gradient(180deg, var(--accent-2), #ffeaf4);
            box-shadow: 0 3px 10px rgba(255, 130, 180, 0.25), inset 0 1px 0 #fff;
            color: #7b4e63;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 130, 180, 0.35), inset 0 1px 0 #fff;
        }

        .btn:active {
            transform: translateY(1px);
        }

        /* 3D stage */
        .stage {
            position: relative;
            transform-style: preserve-3d;
            height: 80vh;
            min-height: 600px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin: 20px 0;
        }

        .helix {
            position: absolute;
            transform-style: preserve-3d;
            width: 0;
            height: 0;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: drift 60s linear infinite;
        }

        @keyframes drift {
            from {
                transform: rotateY(0deg) rotateX(8deg);
            }

            to {
                transform: rotateY(360deg) rotateX(8deg);
            }
        }

        .photo {
            position: absolute;
            width: var(--card-w);
            height: var(--card-h);
            border-radius: 18px;
            overflow: hidden;
            box-shadow: var(--shadow);
            background: #fff8fd;
            outline: 1px solid #ffebf5;
            transform-style: preserve-3d;
            transition: transform .35s ease, box-shadow .35s ease, filter .35s ease;
            will-change: transform;
        }

        .photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            transition: transform 0.5s ease;
        }

        .photo::after {
            content: "";
            position: absolute;
            inset: 0;
            background: linear-gradient(180deg, rgba(255, 255, 255, .35), rgba(255, 255, 255, 0) 60%);
            pointer-events: none;
            mix-blend-mode: screen;
        }

        .photo:hover {
            transform: translateZ(40px) scale(1.08);
            box-shadow: 0 20px 40px rgba(255, 120, 180, 0.35);
            z-index: 1000;
        }

        .photo:hover img {
            transform: scale(1.05);
        }

        /* Message card */
        .message {
            position: absolute;
            left: 50%;
            bottom: clamp(10px, 6vh, 40px);
            transform: translateX(-50%);
            width: min(900px, calc(100% - 32px));
            background: var(--glass);
            backdrop-filter: blur(8px) saturate(125%);
            border: 1px solid #ffe3f1;
            padding: clamp(14px, 3.2vw, 22px);
            border-radius: 20px;
            box-shadow: 0 12px 30px rgba(160, 80, 120, 0.20);
            z-index: 100;
            display: none;
        }

        .message.show {
            display: block;
            animation: fadeIn 0.8s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        .message h2 {
            margin: 0 0 12px 0;
            font-family: "Great Vibes", cursive;
            font-size: clamp(32px, 5vw, 48px);
            color: #c34d7f;
            text-align: center;
        }

        .message-content {
            width: 100%;
            min-height: 120px;
            border: none;
            outline: none;
            background: #ffffffb8;
            border-radius: 12px;
            padding: 16px 18px;
            font: inherit;
            line-height: 1.6;
            color: var(--ink);
            font-family: 'Playfair Display', serif;
            font-size: 18px;
            text-align: center;
            overflow-y: auto;
            max-height: 200px;
        }

        .hint {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 8px;
            text-align: center;
        }

        footer {
            padding: 10px clamp(16px, 4vw, 40px);
            font-size: 14px;
            opacity: 0.8;
            text-align: center;
            margin-top: 10px;
        }

        /* Mobile tweaks */
        @media (max-width: 820px) {
            :root {
                --radius: 190px;
                --card-w: 130px;
                --card-h: 130px;
                --gap-y: 28px;
            }

            header {
                padding-top: 15px;
            }

            .controls {
                margin-top: 8px;
            }
        }

        /* Floating hearts */
        .hearts {
            position: absolute;
            inset: 0;
            pointer-events: none;
            overflow: hidden;
            z-index: 1;
        }

        .heart {
            position: absolute;
            font-size: 18px;
            opacity: .25;
            animation: floatUp linear forwards;
            z-index: 1;
        }

        @keyframes floatUp {
            from {
                transform: translateY(0) scale(0.8) rotate(0deg);
                opacity: .0;
            }

            10% {
                opacity: .25;
            }

            to {
                transform: translateY(-120vh) scale(1.2) rotate(20deg);
                opacity: 0;
            }
        }

        /* Romantic overlay */
        .romantic-overlay {
            position: absolute;
            inset: 0;
            pointer-events: none;
            background: radial-gradient(circle at 20% 30%, rgba(255, 220, 240, 0.3) 0%, transparent 40%),
                radial-gradient(circle at 80% 70%, rgba(255, 200, 230, 0.3) 0%, transparent 40%);
            z-index: 2;
        }

        /* Sparkle effects */
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.8);
            animation: sparkle 2s linear infinite;
            opacity: 0;
        }

        @keyframes sparkle {
            0% {
                opacity: 0;
                transform: scale(0) rotate(0deg);
            }

            50% {
                opacity: 1;
                transform: scale(1) rotate(180deg);
            }

            100% {
                opacity: 0;
                transform: scale(0) rotate(360deg);
            }
        }

        /* Fullscreen message overlay */
        .fullscreen-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 230, 242, 0.95);
            backdrop-filter: blur(5px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: opacity 0.8s ease, transform 0.8s ease;
            padding: 20px;
        }

        .anniversary-title {
            font-family: "Great Vibes", cursive;
            font-size: clamp(48px, 8vw, 80px);
            color: #c34d7f;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(195, 77, 127, 0.3);
        }

        .quiz-container {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(160, 80, 120, 0.2);
            max-width: 500px;
            width: 90%;
            text-align: center;
            margin-bottom: 20px;
        }

        .quiz-question {
            font-family: 'Playfair Display', serif;
            font-size: 22px;
            color: #7b4e63;
            margin-bottom: 20px;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .quiz-option {
            padding: 12px;
            border: none;
            border-radius: 12px;
            background: linear-gradient(180deg, #ffd1e6, #ffc2dd);
            color: #7b4e63;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(255, 130, 180, 0.3);
        }

        .quiz-option.correct {
            background: linear-gradient(180deg, #c3f0d4, #b0e6c3);
        }

        .quiz-option.incorrect {
            background: linear-gradient(180deg, #ffd1d1, #ffc2c2);
        }

        .romantic-message {
            display: none;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(160, 80, 120, 0.2);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .message-text {
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            line-height: 1.6;
            color: #7b4e63;
            margin-bottom: 25px;
        }

        .close-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 30px;
            background: linear-gradient(180deg, var(--accent-2), #ffeaf4);
            color: #7b4e63;
            font-weight: 600;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 130, 180, 0.25);
        }

        .close-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 130, 180, 0.35);
        }

        .reopen-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 20px;
            border: none;
            border-radius: 30px;
            background: linear-gradient(180deg, var(--accent-2), #ffeaf4);
            color: #7b4e63;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 130, 180, 0.25);
            z-index: 101;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .reopen-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 130, 180, 0.35);
        }

        /* Heart animation */
        .heart-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1001;
        }

        .heart-fall {
            position: absolute;
            font-size: 24px;
            opacity: 0;
            animation: heartFall linear forwards;
        }

        @keyframes heartFall {
            0% {
                transform: translateY(-10px) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 0.8;
            }

            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Quiz navigation */
        .quiz-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .nav-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            background: linear-gradient(180deg, var(--accent-2), #ffeaf4);
            color: #7b4e63;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 130, 180, 0.3);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .question-counter {
            font-family: 'Quicksand', sans-serif;
            font-size: 16px;
            color: #7b4e63;
            margin-top: 10px;
        }

        /* Accessibility: prefers-reduced-motion */
        @media (prefers-reduced-motion: reduce) {
            .helix {
                animation: none;
            }

            .heart,
            .sparkle,
            .heart-fall {
                display: none;
            }

            .photo {
                transition: none;
            }

            .fullscreen-overlay {
                transition: none;
            }
        }
    </style>
</head>

<body>
    <div class="wrap">
        <header>
            <h1 class="title" aria-label="Happy Anniversary My Love">Happy Anniversary My Love</h1>
            <div class="subtitle">Celebrating our journey together</div>
            <div class="controls">
                <button class="btn" id="shuffleBtn" type="button" title="Shuffle photos">🔀 Shuffle Memories</button>
                <button class="btn" id="pauseBtn" type="button" title="Pause / Play">⏸️ Pause Motion</button>
                <button class="btn" id="messageBtn" type="button" title="Show Message">💌 Read My Message</button>
            </div>
            <div class="subtle" id="counter" aria-live="polite"></div>
        </header>

        <main class="stage" aria-label="Rotating photo spiral of our love">
            <div class="romantic-overlay"></div>
            <div class="helix" id="helix" aria-hidden="false"></div>
            <div class="hearts" id="hearts" aria-hidden="true"></div>
        </main>

        <div class="message" role="group" aria-labelledby="msg-title" id="floatingMessage">
            <h2 id="msg-title">My Heart to Yours</h2>
            <div id="note" class="message-content">
                My love, from the moment our souls connected, I knew I had found my forever. Each day with you is a
                blessing I never take for granted. Through all of life's seasons, my love for you only grows stronger.
                You are my sunshine, my comfort, my home. Today and always, I choose you. Happy anniversary, my darling.
            </div>
            <div class="hint">This message was crafted with all my love just for you. 💞</div>
        </div>

        <footer>Made with infinite love and eternal devotion</footer>
    </div>

    <!-- Fullscreen overlay with quiz -->
    <div class="fullscreen-overlay" id="fullscreenOverlay">
        <h1 class="anniversary-title">Our Love Story Quiz</h1>

        <div class="quiz-container" id="quizContainer">
            <div class="quiz-question" id="question">Do you remember our first date? 💕</div>
            <div class="quiz-options" id="options">
                <button class="quiz-option" data-correct="false">At the movies</button>
                <button class="quiz-option" data-correct="true">Coffee shop downtown</button>
                <button class="quiz-option" data-correct="false">Beach sunset</button>
                <button class="quiz-option" data-correct="false">Italian restaurant</button>
            </div>

            <div class="quiz-navigation">
                <button class="nav-btn" id="prevQuestion" disabled>Previous</button>
                <div class="question-counter" id="questionCounter">Question 1 of 5</div>
                <button class="nav-btn" id="nextQuestion">Next</button>
            </div>

            <div class="hint">Answer all questions correctly to reveal my special message</div>
        </div>

        <div class="romantic-message" id="romanticMessage">
            <div class="message-text">
                My dearest, from the moment our eyes met, I knew my heart had found its home. Every day with you is a
                gift I cherish more than words can express. Through all of life's seasons, my love for you only grows
                stronger. You are my sunshine, my comfort, my everything. Today and always, I choose you. Happy
                anniversary, my love.
            </div>
            <button class="close-btn" id="closeMessage">Continue to Our Memories</button>
        </div>
    </div>

    <!-- Reopen message button (initially hidden) -->
    <button class="reopen-btn" id="reopenMessage" style="display: none;">
        <span>💖</span> Our Message
    </button>

    <!-- Heart animation container -->
    <div class="heart-animation" id="heartAnimation"></div>

    <script>
        /* ====== CONFIGURE YOUR PHOTOS HERE ======
           Replace the URLs below with your own romantic images.
           You can add as many as you want. */
        let imageUrls = [
            // Replace these with your actual images
            "love.png",
            "love2.png",
            "love3.png",
            "love4.png",
            "love5.png",
            "love6.png",
            "love7.png",
            "love8.png",
            "love9.png",
            "love10.png"
        ];

        /* ====== QUIZ QUESTIONS ====== */
        const quizQuestions = [
            {
                question: "Do you remember our first date? 💕",
                options: [
                    { text: "At the movies", correct: false },
                    { text: "Coffee shop downtown", correct: true },
                    { text: "Beach sunset", correct: false },
                    { text: "Italian restaurant", correct: false }
                ]
            },
            {
                question: "What was the first gift I ever gave you? 🎁",
                options: [
                    { text: "A book", correct: false },
                    { text: "A necklace", correct: true },
                    { text: "Flowers", correct: false },
                    { text: "A handwritten letter", correct: false }
                ]
            },
            {
                question: "Where did we have our first vacation together? ✈️",
                options: [
                    { text: "Paris", correct: false },
                    { text: "Hawaii", correct: true },
                    { text: "New York", correct: false },
                    { text: "Italy", correct: false }
                ]
            },
            {
                question: "What's my favorite thing about you? 🌟",
                options: [
                    { text: "Your smile", correct: false },
                    { text: "Your kindness", correct: false },
                    { text: "Your intelligence", correct: false },
                    { text: "Everything about you", correct: true }
                ]
            },
            {
                question: "What song reminds me of us? 🎵",
                options: [
                    { text: "Perfect by Ed Sheeran", correct: true },
                    { text: "All of Me by John Legend", correct: false },
                    { text: "A Thousand Years by Christina Perri", correct: false },
                    { text: "Can't Help Falling in Love by Elvis", correct: false }
                ]
            }
        ];

        /* ====== DOM Elements ====== */
        const helixEl = document.getElementById('helix');
        const counter = document.getElementById('counter');
        const pauseBtn = document.getElementById('pauseBtn');
        const shuffleBtn = document.getElementById('shuffleBtn');
        const messageBtn = document.getElementById('messageBtn');
        const floatingMessage = document.getElementById('floatingMessage');
        const fullscreenOverlay = document.getElementById('fullscreenOverlay');
        const quizContainer = document.getElementById('quizContainer');
        const romanticMessage = document.getElementById('romanticMessage');
        const closeMessageBtn = document.getElementById('closeMessage');
        const reopenMessageBtn = document.getElementById('reopenMessage');
        const heartAnimation = document.getElementById('heartAnimation');
        const questionEl = document.getElementById('question');
        const optionsEl = document.getElementById('options');
        const prevQuestionBtn = document.getElementById('prevQuestion');
        const nextQuestionBtn = document.getElementById('nextQuestion');
        const questionCounter = document.getElementById('questionCounter');

        let paused = false;
        let currentQuestion = 0;
        let correctAnswers = 0;

        /* ====== Spiral / Helix Setup ====== */
        const state = {
            radius: parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--radius')),
            gapY: parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--gap-y')),
            cards: [],
            t: 0
        };

        function clearHelix() {
            helixEl.innerHTML = "";
            state.cards = [];
        }

        function createCard(src, index) {
            const d = document.createElement('div');
            d.className = 'photo';
            d.setAttribute('tabindex', '0');
            d.setAttribute('aria-label', `Our memory ${index + 1}`);
            const img = document.createElement('img');
            img.src = src;
            img.alt = "Our beautiful memory together";
            d.appendChild(img);
            helixEl.appendChild(d);

            // Add sparkle effect to each photo
            if (index % 3 === 0) {
                addSparkles(d);
            }

            return d;
        }

        // Add sparkle effects to elements
        function addSparkles(element) {
            for (let i = 0; i < 3; i++) {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';
                sparkle.style.left = Math.random() * 90 + 5 + '%';
                sparkle.style.top = Math.random() * 90 + 5 + '%';
                sparkle.style.animationDelay = Math.random() * 2 + 's';
                element.appendChild(sparkle);
            }
        }

        // Golden angle creates a pretty spiral distribution
        const GOLDEN_ANGLE = Math.PI * (3 - Math.sqrt(5)); // ~2.399963

        function layoutHelix() {
            const N = state.cards.length;
            const radius = state.radius;
            const gapY = state.gapY;
            const mid = (N - 1) / 2;

            for (let i = 0; i < N; i++) {
                const theta = i * GOLDEN_ANGLE + state.t * 0.00065; // slow drift
                const y = (i - mid) * gapY + Math.sin((state.t / 900) + i * 0.7) * 8; // gentle bob
                // Position each card around Y-axis, then bring it back facing front
                const tr = `rotateY(${theta}rad) translateZ(${radius}px) translateY(${y}px) rotateY(${-theta}rad)`;
                state.cards[i].style.transform = tr;
            }
        }

        function buildFromUrls(urls) {
            clearHelix();
            urls.forEach((u, i) => state.cards.push(createCard(u, i)));
            counter.textContent = `${urls.length} cherished memories`;
            // small delay lets images paint before first layout
            requestAnimationFrame(() => { layoutHelix(); });
        }

        function animate(ts) {
            if (!paused) {
                state.t = ts || (state.t + 16);
                layoutHelix();
            }
            requestAnimationFrame(animate);
        }
        requestAnimationFrame(animate);

        /* ====== Controls ====== */
        pauseBtn.addEventListener('click', () => {
            paused = !paused;
            helixEl.style.animationPlayState = paused ? 'paused' : 'running';
            pauseBtn.textContent = paused ? '▶️ Resume Motion' : '⏸️ Pause Motion';
            pauseBtn.style.background = paused ?
                'linear-gradient(180deg, #c3f0d4, #b0e6c3)' :
                'linear-gradient(180deg, var(--accent-2), #ffeaf4)';
        });

        shuffleBtn.addEventListener('click', () => {
            for (let i = imageUrls.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [imageUrls[i], imageUrls[j]] = [imageUrls[j], imageUrls[i]];
            }
            buildFromUrls(imageUrls);

            // Visual feedback for shuffle
            shuffleBtn.style.background = 'linear-gradient(180deg, #d4ffed, #c0f8df)';
            setTimeout(() => {
                shuffleBtn.style.background = 'linear-gradient(180deg, var(--accent-2), #ffeaf4)';
            }, 500);
        });

        messageBtn.addEventListener('click', () => {
            floatingMessage.classList.toggle('show');
            messageBtn.textContent = floatingMessage.classList.contains('show')
                ? '❌ Close Message'
                : '💌 Read My Message';
        });

        /* ====== Floating hearts (enhanced) ====== */
        const hearts = document.getElementById('hearts');
        function spawnHeart() {
            const heartTypes = ['💗', '❤️', '💕', '💞', '💓', '💖'];
            const h = document.createElement('div');
            h.className = 'heart';
            h.textContent = heartTypes[Math.floor(Math.random() * heartTypes.length)];
            const x = Math.random() * 100;
            const dur = 8000 + Math.random() * 6000;
            h.style.left = x + 'vw';
            h.style.bottom = '-5vh';
            h.style.animationDuration = dur + 'ms';
            h.style.fontSize = (14 + Math.random() * 16) + 'px';
            hearts.appendChild(h);
            setTimeout(() => h.remove(), dur + 100);
        }
        setInterval(spawnHeart, 800);

        /* ====== Fullscreen Message & Quiz ====== */
        // Create heart fall animation
        function createHeartFall() {
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const heart = document.createElement('div');
                    heart.className = 'heart-fall';
                    heart.textContent = '💖';
                    heart.style.left = Math.random() * 100 + 'vw';
                    heart.style.fontSize = (20 + Math.random() * 20) + 'px';
                    heart.style.animationDuration = (4000 + Math.random() * 4000) + 'ms';
                    heartAnimation.appendChild(heart);

                    // Remove heart after animation completes
                    setTimeout(() => {
                        heart.remove();
                    }, 8000);
                }, i * 200);
            }
        }

        // Load question
        function loadQuestion(index) {
            const question = quizQuestions[index];
            questionEl.textContent = question.question;

            // Clear previous options
            optionsEl.innerHTML = '';

            // Add new options
            question.options.forEach(option => {
                const button = document.createElement('button');
                button.className = 'quiz-option';
                button.textContent = option.text;
                button.setAttribute('data-correct', option.correct);
                button.addEventListener('click', handleAnswer);
                optionsEl.appendChild(button);
            });

            // Update question counter
            questionCounter.textContent = `Question ${index + 1} of ${quizQuestions.length}`;

            // Update navigation buttons
            prevQuestionBtn.disabled = index === 0;
            nextQuestionBtn.disabled = index === quizQuestions.length - 1;
        }

        // Handle answer selection
        function handleAnswer(e) {
            const isCorrect = e.target.getAttribute('data-correct') === 'true';

            if (isCorrect) {
                // Correct answer
                e.target.classList.add('correct');
                correctAnswers++;

                // Move to next question after a delay if not the last question
                if (currentQuestion < quizQuestions.length - 1) {
                    setTimeout(() => {
                        currentQuestion++;
                        loadQuestion(currentQuestion);
                    }, 1000);
                } else {
                    // Last question answered correctly
                    setTimeout(() => {
                        quizContainer.style.display = 'none';
                        romanticMessage.style.display = 'block';
                    }, 1000);
                }
            } else {
                // Incorrect answer
                e.target.classList.add('incorrect');
                const originalText = e.target.textContent;
                e.target.textContent = 'Try again 💔';
                setTimeout(() => {
                    e.target.classList.remove('incorrect');
                    e.target.textContent = originalText;
                }, 1500);
            }
        }

        // Navigation between questions
        prevQuestionBtn.addEventListener('click', () => {
            if (currentQuestion > 0) {
                currentQuestion--;
                loadQuestion(currentQuestion);
            }
        });

        nextQuestionBtn.addEventListener('click', () => {
            if (currentQuestion < quizQuestions.length - 1) {
                currentQuestion++;
                loadQuestion(currentQuestion);
            }
        });

        // Close message and show spiral
        closeMessageBtn.addEventListener('click', () => {
            fullscreenOverlay.style.opacity = '0';
            setTimeout(() => {
                fullscreenOverlay.style.display = 'none';
                reopenMessageBtn.style.display = 'block';
            }, 800);
        });

        // Reopen message
        reopenMessageBtn.addEventListener('click', () => {
            fullscreenOverlay.style.display = 'flex';
            setTimeout(() => {
                fullscreenOverlay.style.opacity = '1';
            }, 10);

            // Reset quiz
            currentQuestion = 0;
            correctAnswers = 0;
            quizContainer.style.display = 'block';
            romanticMessage.style.display = 'none';
            loadQuestion(0);
        });

        /* ====== Initial build ====== */
        buildFromUrls(imageUrls);

        // Start heart animation on page load
        createHeartFall();

        // Load first question
        loadQuestion(0);

        /* ====== Responsiveness: update radius on resize ====== */
        const mq = window.matchMedia("(max-width: 820px)");
        function updateRadius() {
            state.radius = parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--radius'));
            state.gapY = parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--gap-y'));
            layoutHelix();
        }
        mq.addEventListener?.("change", updateRadius);
        window.addEventListener('resize', updateRadius);

        /* ====== Spiral is now centered via CSS ====== */
    </script>
</body>

</html>